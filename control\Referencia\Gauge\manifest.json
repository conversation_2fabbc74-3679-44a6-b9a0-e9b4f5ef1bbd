{"mver": "1.2.0", "control": {"identity": {"name": "Gauge", "version": "1.0", "displayname": "Gauge", "icon": "./assets/icon.png", "type": "guid://df41e50a-e5e9-41b7-aa00-707ec728e695", "start": "./control/index.html"}, "metadata": {"author": "<PERSON>", "keywords": ["Gauge"]}, "contracts": {"api": {"methods": {}, "events": {"Update": {"arguments": {"valor": {"type": "string"}}}}, "properties": {"Data": {"type": "string"}}}}, "types": {}}}