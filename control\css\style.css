body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: rgb(234, 241, 247);
        }
        
        .container {
            display: grid;
            grid-template-columns: 25% 45% 30%;
            grid-template-rows: auto;
            gap: 15px;
            height: 812px;
            width: 1856px;
        }
                
        .title {
            color: black;
            padding: 8px;
            margin: -10px -10px 10px -10px;
            border-radius: 3px 3px 0 0;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .box {
            border-radius: 5px;
            padding: 10px;
        }

        .cards {
            grid-column: 2;
            grid-row: 1;
            height: 390px;
        }

        .grafico {
            grid-column: 2;
            grid-row: 2;
            height: 390px;
            background-color: white;
        }

        .metricas {
            grid-column: 3;
            grid-row: 1;
            height: 390px;
            background-color: white;
        }

        .gauge {
            grid-column: 3;
            grid-row: 2;
            height: 390px;
            background-color: white;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            height: calc(100% - 40px);
        }
        
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

