body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: rgb(234, 241, 247);
        }
        
        .container {
            display: grid;
            grid-template-columns: 20% 50% 30%;
            grid-template-rows: auto;
            gap: 15px;
            height: 812px;
            width: 1856px;
        }
        
        .box {
            border-radius: 5px;
            padding: 10px;
            background-color: rgb(234, 241, 247);
        }
        
        .title {
            color: black;
            padding: 8px;
            margin: -10px -10px 10px -10px;
            border-radius: 3px 3px 0 0;
            font-weight: bold;
        }
        
        .lista {
            grid-column: 1;
            grid-row: 1 / span 2;
            overflow: auto;
            background-color: white;
        }
        
        .cards {
            grid-column: 2;
            grid-row: 1;
            height: 390px;
        }

        .grafico {
            grid-column: 2;
            grid-row: 2;
            height: 390px;
            background-color: white;
        }

        .metricas {
            grid-column: 3;
            grid-row: 1;
            height: 390px;
            background-color: white;
        }

        .gauge {
            grid-column: 3;
            grid-row: 2;
            height: 390px;
            background-color: white;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            height: calc(100% - 40px);
        }
        
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        /* Estilos para a Seção L - Lista/Tabela */
        .table-container {
            height: calc(100% - 50px);
            overflow-y: auto;
            padding: 0 5px;
        }

        .modern-table {
            margin-bottom: 0;
            font-size: 0.9rem;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table-header th {
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 12px 8px;
            border: none;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .modern-table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e9ecef;
        }

        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .modern-table tbody td {
            padding: 10px 8px;
            vertical-align: middle;
            border: none;
            text-align: center;
        }

        .modern-table tbody td:first-child {
            font-weight: 500;
            color: #495057;
            text-align: left;
            padding-left: 12px;
        }

        .modern-table tbody td:nth-child(2) {
            font-weight: 600;
            color: #28a745;
            font-size: 1rem;
        }

        .modern-table tbody td:last-child {
            color: #6c757d;
            font-size: 0.85rem;
            font-style: italic;
        }

        /* Scrollbar personalizada */
        .table-container::-webkit-scrollbar {
            width: 6px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Animação para as linhas da tabela */
        @keyframes fadeInRow {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-row {
            animation: fadeInRow 0.5s ease-out forwards;
        }