/*!
 * chartjs-adapter-date-fns v3.0.0
 * https://www.chartjs.org
 * (c) 2022 chartjs-adapter-date-fns Contributors
 * Released under the MIT license
 */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("chart.js"),require("date-fns")):"function"==typeof define&&define.amd?define(["chart.js","date-fns"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).Chart,e.dateFns)}(this,(function(e,r){"use strict";const t={datetime:"MMM d, yyyy, h:mm:ss aaaa",millisecond:"h:mm:ss.SSS aaaa",second:"h:mm:ss aaaa",minute:"h:mm aaaa",hour:"ha",day:"MMM d",week:"PP",month:"MMM yyyy",quarter:"qqq - yyyy",year:"yyyy"};e._adapters._date.override({_id:"date-fns",formats:function(){return t},parse:function(e,t){if(null==e)return null;const n=typeof e;return"number"===n||e instanceof Date?e=r.toDate(e):"string"===n&&(e="string"==typeof t?r.parse(e,t,new Date,this.options):r.parseISO(e,this.options)),r.isValid(e)?e.getTime():null},format:function(e,t){return r.format(e,t,this.options)},add:function(e,t,n){switch(n){case"millisecond":return r.addMilliseconds(e,t);case"second":return r.addSeconds(e,t);case"minute":return r.addMinutes(e,t);case"hour":return r.addHours(e,t);case"day":return r.addDays(e,t);case"week":return r.addWeeks(e,t);case"month":return r.addMonths(e,t);case"quarter":return r.addQuarters(e,t);case"year":return r.addYears(e,t);default:return e}},diff:function(e,t,n){switch(n){case"millisecond":return r.differenceInMilliseconds(e,t);case"second":return r.differenceInSeconds(e,t);case"minute":return r.differenceInMinutes(e,t);case"hour":return r.differenceInHours(e,t);case"day":return r.differenceInDays(e,t);case"week":return r.differenceInWeeks(e,t);case"month":return r.differenceInMonths(e,t);case"quarter":return r.differenceInQuarters(e,t);case"year":return r.differenceInYears(e,t);default:return 0}},startOf:function(e,t,n){switch(t){case"second":return r.startOfSecond(e);case"minute":return r.startOfMinute(e);case"hour":return r.startOfHour(e);case"day":return r.startOfDay(e);case"week":return r.startOfWeek(e);case"isoWeek":return r.startOfWeek(e,{weekStartsOn:+n});case"month":return r.startOfMonth(e);case"quarter":return r.startOfQuarter(e);case"year":return r.startOfYear(e);default:return e}},endOf:function(e,t){switch(t){case"second":return r.endOfSecond(e);case"minute":return r.endOfMinute(e);case"hour":return r.endOfHour(e);case"day":return r.endOfDay(e);case"week":return r.endOfWeek(e);case"month":return r.endOfMonth(e);case"quarter":return r.endOfQuarter(e);case"year":return r.endOfYear(e);default:return e}}})}));
