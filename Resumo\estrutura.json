{"dt": "2025-06-04 16:39:40", "coletores": [{"ID": 1, "NOME": "Usina - EPM", "DESCRICAO": "Gerenciador das Usinas Solares", "TIPO": "API", "LOCAL": "Subestação", "ENDERECO": "0", "TAG": "", "NUMERO": 0, "ACTIVE": 1}, {"ID": 2, "NOME": "Usina 1", "DESCRICAO": "Usina Solar do Grêmio", "TIPO": "API", "LOCAL": "Grê<PERSON>", "ENDERECO": "0", "TAG": "", "NUMERO": 0, "ACTIVE": 1}, {"ID": 3, "NOME": "Usina 2", "DESCRICAO": "Usina Solar do SHE", "TIPO": "API", "LOCAL": "SHE", "ENDERECO": "0", "TAG": "", "NUMERO": 0, "ACTIVE": 1}, {"ID": 4, "NOME": "Usina 3", "DESCRICAO": "Usina Solar de Utilidades", "TIPO": "API", "LOCAL": "Utilidades", "ENDERECO": "0", "TAG": "", "NUMERO": 0, "ACTIVE": 1}, {"ID": 5, "NOME": "Coletor 1", "DESCRICAO": "Teste Orkan", "TIPO": "Hardware", "LOCAL": "teste", "ENDERECO": "************", "TAG": "COLETOR_1", "NUMERO": 1, "ACTIVE": 1}, {"ID": 6, "NOME": "Coletor 2", "DESCRICAO": "Teste Orkan", "TIPO": "Hardware", "LOCAL": "teste", "ENDERECO": "************", "TAG": "COLETOR_2", "NUMERO": 2, "ACTIVE": 0}], "equipamentos": [{"ID": 1, "FK_ID_COLETOR": 1, "FK_ID_BIBLIOTECA": 1, "FK_ID_LOCAL": 1, "NOME": "EPM", "TAG": "EPM", "FABRICANTE": "Solis", "MODELO": "EPM", "TIPO": "EPM", "CANAL": " ", "IDSOLIS": "1298491919449270146", "SNSOLIS": "1", "ACTIVE": 1}, {"ID": 2, "FK_ID_COLETOR": 2, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_01_001", "TAG": "inv101", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948042516", "SNSOLIS": "1411080232110034", "ACTIVE": 1}, {"ID": 3, "FK_ID_COLETOR": 2, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_01_002", "TAG": "inv102", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948043538", "SNSOLIS": "1411080232110001", "ACTIVE": 1}, {"ID": 4, "FK_ID_COLETOR": 2, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_01_003", "TAG": "inv103", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948043786", "SNSOLIS": "1411080232110029", "ACTIVE": 1}, {"ID": 5, "FK_ID_COLETOR": 2, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_01_004", "TAG": "inv104", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948043779", "SNSOLIS": "1411080232110005", "ACTIVE": 1}, {"ID": 6, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_001", "TAG": "inv201", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948064679", "SNSOLIS": "1416030233080048", "ACTIVE": 1}, {"ID": 7, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_002", "TAG": "inv202", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948065382", "SNSOLIS": "1416030233080041", "ACTIVE": 1}, {"ID": 8, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_003", "TAG": "inv203", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948065477", "SNSOLIS": "1416030233080019", "ACTIVE": 1}, {"ID": 9, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_004", "TAG": "inv204", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948065905", "SNSOLIS": "1416030233080008", "ACTIVE": 1}, {"ID": 10, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_005", "TAG": "inv205", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948064648", "SNSOLIS": "1416030233080089", "ACTIVE": 1}, {"ID": 11, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_006", "TAG": "inv206", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948064722", "SNSOLIS": "1416030233080094", "ACTIVE": 1}, {"ID": 12, "FK_ID_COLETOR": 3, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_02_007", "TAG": "inv207", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217948065668", "SNSOLIS": "1416030233080032", "ACTIVE": 1}, {"ID": 13, "FK_ID_COLETOR": 4, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_03_001", "TAG": "inv301", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217947675619", "SNSOLIS": "1180E0229170127", "ACTIVE": 1}, {"ID": 14, "FK_ID_COLETOR": 4, "FK_ID_BIBLIOTECA": 2, "FK_ID_LOCAL": 1, "NOME": "Usina_03_002", "TAG": "inv302", "FABRICANTE": "BYD", "MODELO": "Solis", "TIPO": "Inversor", "CANAL": " ", "IDSOLIS": "1308675217947307266", "SNSOLIS": "1143D2225100099", "ACTIVE": 1}], "canais": [{"ID": 1, "TIPO": "CONTADOR", "NUMERO": 1}, {"ID": 2, "TIPO": "CONTADOR", "NUMERO": 2}, {"ID": 3, "TIPO": "CONTADOR", "NUMERO": 3}, {"ID": 4, "TIPO": "CONTADOR", "NUMERO": 4}, {"ID": 5, "TIPO": "CONTADOR", "NUMERO": 5}, {"ID": 6, "TIPO": "CONTADOR", "NUMERO": 6}, {"ID": 7, "TIPO": "DIGITAL", "NUMERO": 1}, {"ID": 8, "TIPO": "DIGITAL", "NUMERO": 2}, {"ID": 9, "TIPO": "DIGITAL", "NUMERO": 3}, {"ID": 10, "TIPO": "DIGITAL", "NUMERO": 4}, {"ID": 11, "TIPO": "DIGITAL", "NUMERO": 5}, {"ID": 12, "TIPO": "DIGITAL", "NUMERO": 6}, {"ID": 13, "TIPO": "DIGITAL", "NUMERO": 7}, {"ID": 14, "TIPO": "DIGITAL", "NUMERO": 8}, {"ID": 15, "TIPO": "ANALOGICO", "NUMERO": 1}, {"ID": 16, "TIPO": "ANALOGICO", "NUMERO": 2}, {"ID": 17, "TIPO": "ANALOGICO", "NUMERO": 3}, {"ID": 18, "TIPO": "ANALOGICO", "NUMERO": 4}, {"ID": 19, "TIPO": "ANALOGICO", "NUMERO": 5}, {"ID": 20, "TIPO": "ANALOGICO", "NUMERO": 6}, {"ID": 21, "TIPO": "ANALOGICO", "NUMERO": 7}, {"ID": 22, "TIPO": "ANALOGICO", "NUMERO": 8}, {"ID": 23, "TIPO": "REDE", "NUMERO": 1}, {"ID": 24, "TIPO": "REDE", "NUMERO": 2}, {"ID": 25, "TIPO": "REDE", "NUMERO": 3}, {"ID": 26, "TIPO": "REDE", "NUMERO": 4}, {"ID": 27, "TIPO": "REDE", "NUMERO": 5}, {"ID": 28, "TIPO": "REDE", "NUMERO": 6}, {"ID": 29, "TIPO": "REDE", "NUMERO": 7}, {"ID": 30, "TIPO": "REDE", "NUMERO": 8}, {"ID": 31, "TIPO": "REDE", "NUMERO": 9}, {"ID": 32, "TIPO": "REDE", "NUMERO": 10}, {"ID": 33, "TIPO": "REDE", "NUMERO": 11}, {"ID": 34, "TIPO": "REDE", "NUMERO": 12}, {"ID": 35, "TIPO": "REDE", "NUMERO": 13}, {"ID": 36, "TIPO": "REDE", "NUMERO": 14}, {"ID": 37, "TIPO": "REDE", "NUMERO": 15}, {"ID": 38, "TIPO": "REDE", "NUMERO": 16}, {"ID": 39, "TIPO": "REDE", "NUMERO": 17}, {"ID": 40, "TIPO": "REDE", "NUMERO": 18}, {"ID": 41, "TIPO": "REDE", "NUMERO": 19}, {"ID": 42, "TIPO": "REDE", "NUMERO": 20}, {"ID": 43, "TIPO": "REDE", "NUMERO": 21}, {"ID": 44, "TIPO": "REDE", "NUMERO": 22}, {"ID": 45, "TIPO": "REDE", "NUMERO": 23}, {"ID": 46, "TIPO": "REDE", "NUMERO": 24}, {"ID": 47, "TIPO": "REDE", "NUMERO": 25}, {"ID": 48, "TIPO": "REDE", "NUMERO": 26}, {"ID": 49, "TIPO": "REDE", "NUMERO": 27}, {"ID": 50, "TIPO": "REDE", "NUMERO": 28}, {"ID": 51, "TIPO": "REDE", "NUMERO": 29}, {"ID": 52, "TIPO": "REDE", "NUMERO": 30}, {"ID": 53, "TIPO": "REDE", "NUMERO": 31}, {"ID": 54, "TIPO": "REDE", "NUMERO": 32}], "coletorCanais": [], "variaveis": [], "software": [{"ID": 1, "TELA": "estrutura", "OBJETO": "timestamp", "VALOR": "2025-06-04 16:39:40", "ACTIVE": 1}, {"ID": 2, "TELA": "coletor", "OBJETO": "comboTipo", "VALOR": "API", "ACTIVE": 1}, {"ID": 3, "TELA": "coletor", "OBJETO": "comboTipo", "VALOR": "Hardware", "ACTIVE": 1}, {"ID": 4, "TELA": "equipamento", "OBJETO": "comboTipoAPI", "VALOR": "EPM", "ACTIVE": 1}, {"ID": 5, "TELA": "equipamento", "OBJETO": "comboTipoAPI", "VALOR": "Inversor", "ACTIVE": 1}, {"ID": 6, "TELA": "equipamento", "OBJETO": "comboTipoHardware", "VALOR": "Sensor V<PERSON>", "ACTIVE": 1}, {"ID": 7, "TELA": "equipamento", "OBJETO": "comboTipoHardware", "VALOR": "Multimedidor", "ACTIVE": 1}, {"ID": 8, "TELA": "equipamento", "OBJETO": "comboCanal", "VALOR": "IO", "ACTIVE": 1}, {"ID": 9, "TELA": "equipamento", "OBJETO": "comboCanal", "VALOR": "Rede", "ACTIVE": 1}, {"ID": 10, "TELA": "variavelRede", "OBJETO": "parameter1", "VALOR": "Endereço Slave", "ACTIVE": 1}, {"ID": 11, "TELA": "variavelRede", "OBJETO": "parameter2", "VALOR": "Endereço Inicial", "ACTIVE": 1}, {"ID": 12, "TELA": "variavelRede", "OBJETO": "parameter3", "VALOR": "Length", "ACTIVE": 1}, {"ID": 13, "TELA": "variavelRede", "OBJETO": "parameter4", "VALOR": "Estrutura", "ACTIVE": 1}, {"ID": 14, "TELA": "variavelRede", "OBJETO": "parameter5", "VALOR": "Formato", "ACTIVE": 1}, {"ID": 15, "TELA": "variavelAnalogica", "OBJETO": "parameter1", "VALOR": "Scale Max", "ACTIVE": 1}, {"ID": 16, "TELA": "variavelAnalogica", "OBJETO": "parameter2", "VALOR": "Scale Min", "ACTIVE": 1}, {"ID": 17, "TELA": "variavelContador", "OBJETO": "parameter1", "VALOR": "<PERSON><PERSON>", "ACTIVE": 1}, {"ID": 18, "TELA": "estruturaRede", "OBJETO": "Int", "VALOR": "1", "ACTIVE": 1}, {"ID": 19, "TELA": "estruturaRede", "OBJETO": "DInt", "VALOR": "2", "ACTIVE": 1}, {"ID": 20, "TELA": "estruturaRede", "OBJETO": "Real", "VALOR": "3", "ACTIVE": 1}, {"ID": 21, "TELA": "formatoRede", "OBJETO": "<PERSON> Endian", "VALOR": "1", "ACTIVE": 1}, {"ID": 22, "TELA": "formatoRede", "OBJETO": "<PERSON>", "VALOR": "2", "ACTIVE": 1}, {"ID": 23, "TELA": "formatoRede", "OBJETO": "Big <PERSON><PERSON>p", "VALOR": "3", "ACTIVE": 1}, {"ID": 24, "TELA": "formatoRede", "OBJETO": "<PERSON> <PERSON><PERSON>p", "VALOR": "4", "ACTIVE": 1}], "biblioteca": [{"ID": 1, "TIPO": "API", "NOME": "EPM", "ACTIVE": 1}, {"ID": 2, "TIPO": "API", "NOME": "Inversor", "ACTIVE": 1}, {"ID": 3, "TIPO": "Hardware", "NOME": "Consumo <PERSON> Potável", "ACTIVE": 1}, {"ID": 4, "TIPO": "Hardware", "NOME": "Consumo <PERSON> Industrial", "ACTIVE": 1}, {"ID": 5, "TIPO": "Hardware", "NOME": "Multimedidor de Energia", "ACTIVE": 1}, {"ID": 6, "TIPO": "Hardware", "NOME": "Multimedidor Equipamento", "ACTIVE": 1}], "template": [{"ID": 1, "FK_ID_BIBLIOTECA": 3, "NUMERO": 1, "NOME": "Consumo de Água Potável", "UNIDADE": "m³", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 2, "FK_ID_BIBLIOTECA": 4, "NUMERO": 1, "NOME": "Consumo de Água de Reuso", "UNIDADE": "m³", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 3, "FK_ID_BIBLIOTECA": 5, "NUMERO": 1, "NOME": "Consumo de Energia", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 4, "FK_ID_BIBLIOTECA": 5, "NUMERO": 2, "NOME": "Tensão V1N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 5, "FK_ID_BIBLIOTECA": 5, "NUMERO": 3, "NOME": "Tensão V2N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 6, "FK_ID_BIBLIOTECA": 5, "NUMERO": 4, "NOME": "Tensão V3N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 7, "FK_ID_BIBLIOTECA": 5, "NUMERO": 5, "NOME": "Tensão V12", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 8, "FK_ID_BIBLIOTECA": 5, "NUMERO": 6, "NOME": "Tensão V23", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 9, "FK_ID_BIBLIOTECA": 5, "NUMERO": 7, "NOME": "Tensão V31", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 10, "FK_ID_BIBLIOTECA": 5, "NUMERO": 8, "NOME": "Corrente I1", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 11, "FK_ID_BIBLIOTECA": 5, "NUMERO": 9, "NOME": "Corrente I2", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 12, "FK_ID_BIBLIOTECA": 5, "NUMERO": 10, "NOME": "Corrente I3", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 13, "FK_ID_BIBLIOTECA": 5, "NUMERO": 11, "NOME": "Total KWh", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 14, "FK_ID_BIBLIOTECA": 5, "NUMERO": 12, "NOME": "FP Médio", "UNIDADE": "", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 15, "FK_ID_BIBLIOTECA": 5, "NUMERO": 13, "NOME": "Frequência", "UNIDADE": "Hz", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 16, "FK_ID_BIBLIOTECA": 5, "NUMERO": 14, "NOME": "Total KWh (Imp)", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 17, "FK_ID_BIBLIOTECA": 5, "NUMERO": 15, "NOME": "Total Net KVAh", "UNIDADE": "kVA/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 18, "FK_ID_BIBLIOTECA": 5, "NUMERO": 16, "NOME": "Total Net KVArh", "UNIDADE": "kVAr/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 19, "FK_ID_BIBLIOTECA": 6, "NUMERO": 1, "NOME": "Consumo de Energia", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 20, "FK_ID_BIBLIOTECA": 6, "NUMERO": 2, "NOME": "Tensão V1N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 21, "FK_ID_BIBLIOTECA": 6, "NUMERO": 3, "NOME": "Tensão V2N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 22, "FK_ID_BIBLIOTECA": 6, "NUMERO": 4, "NOME": "Tensão V3N", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 23, "FK_ID_BIBLIOTECA": 6, "NUMERO": 5, "NOME": "Tensão V12", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 24, "FK_ID_BIBLIOTECA": 6, "NUMERO": 6, "NOME": "Tensão V23", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 25, "FK_ID_BIBLIOTECA": 6, "NUMERO": 7, "NOME": "Tensão V31", "UNIDADE": "V", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 26, "FK_ID_BIBLIOTECA": 6, "NUMERO": 8, "NOME": "Corrente I1", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 27, "FK_ID_BIBLIOTECA": 6, "NUMERO": 9, "NOME": "Corrente I2", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 28, "FK_ID_BIBLIOTECA": 6, "NUMERO": 10, "NOME": "Corrente I3", "UNIDADE": "A", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 29, "FK_ID_BIBLIOTECA": 6, "NUMERO": 11, "NOME": "Total KWh", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 30, "FK_ID_BIBLIOTECA": 6, "NUMERO": 12, "NOME": "FP Médio", "UNIDADE": "", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 31, "FK_ID_BIBLIOTECA": 6, "NUMERO": 13, "NOME": "Frequência", "UNIDADE": "Hz", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 32, "FK_ID_BIBLIOTECA": 6, "NUMERO": 14, "NOME": "Total KWh (Imp)", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 33, "FK_ID_BIBLIOTECA": 6, "NUMERO": 15, "NOME": "Total Net KVAh", "UNIDADE": "kVA/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}, {"ID": 34, "FK_ID_BIBLIOTECA": 6, "NUMERO": 16, "NOME": "Total Net KVArh", "UNIDADE": "kW/h", "CASAS": 2, "FATOR": 0, "ACTIVE": 1}], "locais": [{"ID": 1, "DESCRICAO": "", "ACTIVE": 1}, {"ID": 2, "DESCRICAO": "G<PERSON>", "ACTIVE": 1}, {"ID": 3, "DESCRICAO": "Prédio 1", "ACTIVE": 1}, {"ID": 4, "DESCRICAO": "Prédio 2", "ACTIVE": 1}, {"ID": 5, "DESCRICAO": "Prédio 3", "ACTIVE": 1}, {"ID": 6, "DESCRICAO": "Prédio 4", "ACTIVE": 1}, {"ID": 7, "DESCRICAO": "Prédio 5", "ACTIVE": 1}, {"ID": 8, "DESCRICAO": "Chillers", "ACTIVE": 1}, {"ID": 9, "DESCRICAO": "Compressores", "ACTIVE": 1}, {"ID": 10, "DESCRICAO": "ETE", "ACTIVE": 1}]}