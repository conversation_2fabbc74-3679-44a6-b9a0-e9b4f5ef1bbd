// Dashboard de Energia - Script Principal

// Variáveis globais
let chartInstance = null;
let gaugeInstance = null;
let currentPeriod = 'dia';
let lastUpdate = new Date();

let dataList = [
    {
        descricao: "Tensão V1N",
        valor: 220,
        unidade: "V"
    },
    {
        descricao: "Tensão V2N",
        valor: 220,
        unidade: "V"
    },
    {
        descricao: "Tensão V3N",
        valor: 220,
        unidade: "V"
    },
    {
        descricao: "Tensão V12",
        valor: 380,
        unidade: "V"
    },
    {
        descricao: "Tensão V23",
        valor: 380,
        unidade: "V"
    },
    {
        descricao: "Tensão V31",
        valor: 380,
        unidade: "V"
    },
    {
        descricao: "Corrente I1",
        valor: 15.2,
        unidade: "A"
    },
    {
        descricao: "Corrente I2",
        valor: 14.8,
        unidade: "A"
    },
    {
        descricao: "Corrente I3",
        valor: 16.1,
        unidade: "A"
    },
    {
        descricao: "Potência Ativa",
        valor: 12.5,
        unidade: "kW"
    },
    {
        descricao: "Potência Reativa",
        valor: 3.2,
        unidade: "kVAr"
    },
    {
        descricao: "Fator de Potência",
        valor: 0.92,
        unidade: ""
    }
]

// Função para popular a tabela de dados
function populateDataTable(data) {
    const tableBody = document.getElementById('dataTableBody');

    if (!tableBody) {
        console.error('Elemento dataTableBody não encontrado');
        return;
    }

    // Limpa a tabela
    tableBody.innerHTML = '';

    // Adiciona cada item de dados como uma linha
    data.forEach((item, index) => {
        const row = document.createElement('tr');

        // Formata o valor com unidade se existir
        let valorFormatado = typeof item.valor === 'number' ? item.valor.toFixed(2) : item.valor;
        if (item.unidade && item.unidade.trim() !== '') {
            valorFormatado += ` ${item.unidade}`;
        }

        row.innerHTML = `
            <td title="${item.descricao}">${item.descricao}</td>
            <td title="${valorFormatado}">${valorFormatado}</td>
        `;

        // Adiciona animação escalonada
        row.style.animationDelay = `${index * 0.1}s`;
        row.classList.add('fade-in-row');

        tableBody.appendChild(row);
    });
}

// Função para atualizar dados (simula recebimento do WinCC Unified)
function updateDataFromWinCC(jsonData) {
    try {
        // Se jsonData for uma string JSON, converte para array
        const dataArray = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;

        // Atualiza a variável global
        dataList = dataArray;

        // Atualiza a tabela
        populateDataTable(dataList);

        // Atualiza timestamp
        lastUpdate = new Date();

        console.log('Dados atualizados:', dataList);
    } catch (error) {
        console.error('Erro ao processar dados do WinCC:', error);
    }
}

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Popula a tabela inicial
    populateDataTable(dataList);

    console.log('Dashboard de Energia inicializado');
});

