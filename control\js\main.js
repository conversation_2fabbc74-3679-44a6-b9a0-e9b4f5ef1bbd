// Dashboard de Energia - <PERSON>ript Principal

// Variáveis globais
let chartInstance = null;
let gaugeInstance = null;
let currentPeriod = 'dia';
let lastUpdate = new Date();

// Dados de exemplo (serão substituídos por dados reais do WinCC)
const dadosExemplo = {
    energia: {
        dia: [
            { timestamp: '2024-05-03T00:00:00', valor: 10.5 },
            { timestamp: '2024-05-03T01:00:00', valor: 8.2 },
            { timestamp: '2024-05-03T02:00:00', valor: 7.1 },
            { timestamp: '2024-05-03T03:00:00', valor: 6.8 },
            { timestamp: '2024-05-03T04:00:00', valor: 7.3 },
            { timestamp: '2024-05-03T05:00:00', valor: 9.2 },
            { timestamp: '2024-05-03T06:00:00', valor: 15.7 },
            { timestamp: '2024-05-03T07:00:00', valor: 22.3 },
            { timestamp: '2024-05-03T08:00:00', valor: 28.9 },
            { timestamp: '2024-05-03T09:00:00', valor: 32.1 },
            { timestamp: '2024-05-03T10:00:00', valor: 35.6 },
            { timestamp: '2024-05-03T11:00:00', valor: 37.2 },
            { timestamp: '2024-05-03T12:00:00', valor: 36.8 },
            { timestamp: '2024-05-03T13:00:00', valor: 35.4 },
            { timestamp: '2024-05-03T14:00:00', valor: 33.9 },
            { timestamp: '2024-05-03T15:00:00', valor: 32.7 }
        ],
        mes: [
            { timestamp: '2024-05-01', valor: 245.8 },
            { timestamp: '2024-05-02', valor: 237.6 },
            { timestamp: '2024-05-03', valor: 252.3 }
        ],
        ano: [
            { timestamp: '2024-01', valor: 7250 },
            { timestamp: '2024-02', valor: 6890 },
            { timestamp: '2024-03', valor: 7320 },
            { timestamp: '2024-04', valor: 7150 },
            { timestamp: '2024-05', valor: 2450 }
        ]
    },
    metricas: {
        mediasDiarias: [
            { label: 'Média Diária', valor: 245.8, unidade: 'kWh', tendencia: 'up', percentual: 3.5 },
            { label: 'Pico de Demanda', valor: 42.18, unidade: 'kW', tendencia: 'down', percentual: 1.2 },
            { label: 'Fator de Potência', valor: 0.92, unidade: '', tendencia: 'stable', percentual: 0 },
            { label: 'Custo Estimado', valor: 127.45, unidade: 'R$', tendencia: 'up', percentual: 3.14 }
        ]
    },
    gauge: {
        atual: 35.67,
        min: 0,
        max: 100,
        limites: [
            { valor: 30, cor: '#47ad77' },
            { valor: 70, cor: '#ffc35a' },
            { valor: 100, cor: '#fa5c7c' }
        ]
    }
};

// Inicialização quando o documento estiver pronto
$(document).ready(function() {
    console.log('Dashboard de Energia inicializado');
    
    // Verificar se Chart.js está disponível
    if (typeof Chart === 'undefined') {
        console.error('Chart.js não está carregado. Tentando carregar via CDN...');
        
        // Tentar carregar Chart.js via CDN
        const chartScript = document.createElement('script');
        chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js';
        chartScript.onload = function() {
            console.log('Chart.js carregado com sucesso via CDN');
            
            // Carregar o adaptador após o Chart.js
            const adapterScript = document.createElement('script');
            adapterScript.src = 'https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.min.js';
            adapterScript.onload = function() {
                console.log('Adaptador date-fns carregado com sucesso via CDN');
                
                // Inicializar componentes após carregar as bibliotecas
                initChart();
                initGauge();
                updateLastUpdate();
                setupEventListeners();
            };
            document.head.appendChild(adapterScript);
        };
        document.head.appendChild(chartScript);
    } else {
        // Chart.js já está disponível, inicializar normalmente
        initChart();
        initGauge();
        updateLastUpdate();
        setupEventListeners();
    }
    
    // Simular atualização periódica
    setInterval(function() {
        updateDashboard();
    }, 60000); // Atualizar a cada minuto
});

// Configurar listeners de eventos
function setupEventListeners() {
    // Botões de período para o gráfico
    $('#btnDia').on('click', function() {
        setActivePeriod('dia');
    });
    
    $('#btnMes').on('click', function() {
        setActivePeriod('mes');
    });
    
    $('#btnAno').on('click', function() {
        setActivePeriod('ano');
    });
    
    $('#btnGlobal').on('click', function() {
        setActivePeriod('global');
    });
    
    // Itens da lista
    $('.lista-item').on('click', function() {
        $('.lista-item').removeClass('selected');
        $(this).addClass('selected');
    });
}

// Definir período ativo e atualizar gráfico
function setActivePeriod(period) {
    currentPeriod = period;
    $('.period-buttons button').removeClass('active');
    $(`#btn${period.charAt(0).toUpperCase() + period.slice(1)}`).addClass('active');
    updateChart();
}

// Inicializar o gráfico
function initChart() {
    const ctx = document.createElement('canvas');
    $('#chartContainer').empty().append(ctx);
    
    chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Consumo de Energia (kWh)',
                borderColor: '#3e79f7',
                backgroundColor: 'rgba(62, 121, 247, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 3,
                pointBackgroundColor: '#3e79f7'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'hour'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
    
    // Definir período inicial como 'dia'
    setActivePeriod('dia');
}

// Atualizar dados do gráfico com base no período selecionado
function updateChart() {
    if (!chartInstance) return;
    
    let dados = [];
    let timeUnit = 'hour';
    
    switch(currentPeriod) {
        case 'dia':
            dados = dadosExemplo.energia.dia;
            timeUnit = 'hour';
            break;
        case 'mes':
            dados = dadosExemplo.energia.mes;
            timeUnit = 'day';
            break;
        case 'ano':
            dados = dadosExemplo.energia.ano;
            timeUnit = 'month';
            break;
        case 'global':
            // Dados globais (exemplo)
            dados = dadosExemplo.energia.ano;
            timeUnit = 'month';
            break;
    }
    
    // Atualizar configuração de tempo
    chartInstance.options.scales.x.time.unit = timeUnit;
    
    // Atualizar dados
    chartInstance.data.labels = dados.map(item => new Date(item.timestamp));
    chartInstance.data.datasets[0].data = dados.map(item => ({
        x: new Date(item.timestamp),
        y: item.valor
    }));
    
    // Atualizar gráfico
    chartInstance.update();
}

// Inicializar o gauge
function initGauge() {
    const gaugeEl = document.getElementById('gaugeContainer');
    $(gaugeEl).empty();
    
    // Criar elemento para o gauge
    const gaugeChart = document.createElement('div');
    gaugeChart.style.width = '100%';
    gaugeChart.style.height = '100%';
    gaugeEl.appendChild(gaugeChart);
    
    // Inicializar ECharts
    gaugeInstance = echarts.init(gaugeChart);
    
    // Configuração do gauge
    const gaugeOption = {
        series: [{
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            min: dadosExemplo.gauge.min,
            max: dadosExemplo.gauge.max,
            radius: '100%',
            axisLine: {
                lineStyle: {
                    width: 30,
                    color: [
                        [0.3, '#47ad77'],
                        [0.7, '#ffc35a'],
                        [1, '#fa5c7c']
                    ]
                }
            },
            pointer: {
                icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                length: '12%',
                width: 20,
                offsetCenter: [0, '-60%'],
                itemStyle: {
                    color: 'auto'
                }
            },
            axisTick: {
                length: 12,
                lineStyle: {
                    color: 'auto',
                    width: 2
                }
            },
            splitLine: {
                length: 20,
                lineStyle: {
                    color: 'auto',
                    width: 5
                }
            },
            axisLabel: {
                color: '#6c757d',
                fontSize: 12,
                distance: -60,
                formatter: function(value) {
                    if (value === dadosExemplo.gauge.max) {
                        return 'Máx';
                    } else if (value === dadosExemplo.gauge.min) {
                        return 'Mín';
                    }
                    return value;
                }
            },
            title: {
                offsetCenter: [0, '-20%'],
                fontSize: 14,
                color: '#6c757d'
            },
            detail: {
                fontSize: 30,
                offsetCenter: [0, '0%'],
                valueAnimation: true,
                formatter: function(value) {
                    return value.toFixed(2) + ' kW';
                },
                color: '#343a40'
            },
            data: [{
                value: dadosExemplo.gauge.atual,
                name: 'Potência Atual'
            }]
        }]
    };
    
    // Aplicar configuração
    gaugeInstance.setOption(gaugeOption);
    
    // Redimensionar quando a janela for redimensionada
    window.addEventListener('resize', function() {
        gaugeInstance.resize();
    });
}

// Atualizar timestamp da última atualização
function updateLastUpdate() {
    lastUpdate = new Date();
    $('#lastUpdate').text('Last: ' + lastUpdate.toLocaleString());
}

// Atualizar todo o dashboard
function updateDashboard() {
    // Aqui seria feita a requisição para o WinCC para obter dados atualizados
    // Por enquanto, apenas atualizamos o timestamp
    updateLastUpdate();
    
    // Atualizar gráfico
    updateChart();
    
    // Atualizar gauge (simulando uma pequena variação)
    if (gaugeInstance) {
        const novoValor = dadosExemplo.gauge.atual + (Math.random() * 2 - 1);
        gaugeInstance.setOption({
            series: [{
                data: [{
                    value: novoValor
                }]
            }]
        });
        dadosExemplo.gauge.atual = novoValor;
    }
}

// Função para integração com WinCC (a ser implementada)
function connectToWinCC() {
    // Implementação futura para conexão com WinCC
    console.log('Conectando ao WinCC...');
}


