CREATE DATABASE  IF NOT EXISTS `orkandb` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `orkandb`;
-- MySQL dump 10.13  Distrib 8.0.40, for Win64 (x86_64)
--
-- Host: localhost    Database: orkandb
-- ------------------------------------------------------
-- Server version	8.0.40

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tblbiblioteca`
--

DROP TABLE IF EXISTS `tblbiblioteca`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblbiblioteca` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tipo` varchar(45) NOT NULL,
  `nome` varchar(45) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblcanais`
--

DROP TABLE IF EXISTS `tblcanais`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblcanais` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tipo` enum('CONTADOR','DIGITAL','ANALOGICO','REDE') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `numero` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tipo` (`tipo`,`numero`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblcoletorcanais`
--

DROP TABLE IF EXISTS `tblcoletorcanais`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblcoletorcanais` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_coletor` int NOT NULL,
  `fk_id_canal` int NOT NULL,
  `fk_id_equipamento` int NOT NULL,
  `parameter_1` float DEFAULT '0' COMMENT 'Digital => Fator_K\\nAnalogica => Scale_Max\\nRede => Address_Slave',
  `parameter_2` float DEFAULT '0' COMMENT 'Digital => not used\\nAnalogica => Scale_Min\\nRede => Address_Initiial',
  `parameter_3` float DEFAULT '0' COMMENT 'Digital => not used\\nAnalogica => not_used\\nRede => Length',
  `parameter_4` float DEFAULT '0' COMMENT 'Digital => not used\\nAnalogica => not used\\nRede => Data_Sctructure',
  `parameter_5` float DEFAULT '0' COMMENT 'Digital => not used\\nAnalogica => not used\\nRede => Endieness',
  `tempo` int DEFAULT '300',
  `active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `fk_id_coletor` (`fk_id_coletor`,`fk_id_canal`),
  KEY `fk_id_canal` (`fk_id_canal`),
  KEY `tblcoletorcanais_ibfk_3_idx` (`fk_id_equipamento`),
  CONSTRAINT `tblcoletorcanais_ibfk_1` FOREIGN KEY (`fk_id_coletor`) REFERENCES `tblcoletores` (`id`),
  CONSTRAINT `tblcoletorcanais_ibfk_2` FOREIGN KEY (`fk_id_canal`) REFERENCES `tblcanais` (`id`),
  CONSTRAINT `tblcoletorcanais_ibfk_3` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblcoletores`
--

DROP TABLE IF EXISTS `tblcoletores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblcoletores` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(45) NOT NULL,
  `descricao` varchar(45) NOT NULL,
  `tipo` char(45) NOT NULL DEFAULT 'H',
  `local` varchar(45) NOT NULL,
  `tag` varchar(45) DEFAULT NULL,
  `endereco` varchar(45) DEFAULT NULL,
  `numero` int DEFAULT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblequipamentos`
--

DROP TABLE IF EXISTS `tblequipamentos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblequipamentos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `tag` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `fabricante` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `modelo` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `tipo` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `canal` varchar(20) DEFAULT '',
  `idSolis` varchar(20) DEFAULT NULL,
  `snSolis` varchar(45) DEFAULT NULL,
  `fk_id_coletor` int NOT NULL,
  `fk_id_biblioteca` int DEFAULT NULL,
  `fk_id_local` int DEFAULT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `fk_id_coletor_idx` (`fk_id_coletor`),
  KEY `fk_id_biblio_idx` (`fk_id_biblioteca`),
  KEY `fk_id_local_idx` (`fk_id_local`),
  CONSTRAINT `fk_id_biblio` FOREIGN KEY (`fk_id_biblioteca`) REFERENCES `tblbiblioteca` (`id`),
  CONSTRAINT `fk_id_coletor` FOREIGN KEY (`fk_id_coletor`) REFERENCES `tblcoletores` (`id`),
  CONSTRAINT `fk_id_local` FOREIGN KEY (`fk_id_local`) REFERENCES `tbllocais` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tbllocais`
--

DROP TABLE IF EXISTS `tbllocais`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbllocais` (
  `id` int NOT NULL AUTO_INCREMENT,
  `descricao` varchar(45) NOT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Armazena os subgrupos de dashboards';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblprodsolar`
--

DROP TABLE IF EXISTS `tblprodsolar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblprodsolar` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_equipamento` int NOT NULL,
  `dt_gravacao` timestamp NULL DEFAULT NULL,
  `potencia` double DEFAULT NULL,
  `acumulado` double DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `fk_id_equip_idx` (`fk_id_equipamento`),
  CONSTRAINT `fk_id_equip` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=686055 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblproducao`
--

DROP TABLE IF EXISTS `tblproducao`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblproducao` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_variavel` int NOT NULL,
  `valor` float DEFAULT NULL,
  `custo` float DEFAULT NULL,
  `delta` float DEFAULT NULL,
  `dt_gravacao` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_fk_id_variavel_dt_gravacao` (`fk_id_variavel`,`dt_gravacao`),
  KEY `fk_id_variavel_idx` (`fk_id_variavel`),
  KEY `idx_fk_id_variavel_dt_gravacao` (`fk_id_variavel`,`dt_gravacao`),
  CONSTRAINT `fk_id_variavel` FOREIGN KEY (`fk_id_variavel`) REFERENCES `tblvariaveis` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblrepositorio`
--

DROP TABLE IF EXISTS `tblrepositorio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblrepositorio` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(45) NOT NULL,
  `descricao` varchar(45) NOT NULL,
  `active` tinyint DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblsoftware`
--

DROP TABLE IF EXISTS `tblsoftware`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblsoftware` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tela` varchar(45) NOT NULL,
  `objeto` varchar(45) NOT NULL,
  `valor` varchar(45) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Esta tabela será responsável por algumas constantes utilizadas no software de coleta de dados.';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblsolis`
--

DROP TABLE IF EXISTS `tblsolis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblsolis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_equipamento` int DEFAULT NULL,
  `descricao` varchar(45) DEFAULT NULL,
  `valor` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_id_equip_idx` (`fk_id_equipamento`),
  CONSTRAINT `fk_id_equip2` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=836 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblsolisanual`
--

DROP TABLE IF EXISTS `tblsolisanual`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblsolisanual` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_equipamento` int NOT NULL,
  `data` year NOT NULL,
  `energy` decimal(10,2) NOT NULL,
  `energyStr` varchar(4) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `moneyStr` varchar(4) NOT NULL,
  `gridPurchasedEnergy` float DEFAULT NULL,
  `gridPurchasedIncome` float DEFAULT NULL,
  `homeLoadEnergy` float DEFAULT NULL,
  `consumeEnergy` float DEFAULT NULL,
  `produceEnergy` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_fk_id_equip_anual` (`fk_id_equipamento`,`data`),
  KEY `fk_id_equip_anual_idx` (`fk_id_equipamento`),
  CONSTRAINT `fk_id_equip_anual` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=363551 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblsolisdiario`
--

DROP TABLE IF EXISTS `tblsolisdiario`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblsolisdiario` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_equipamento` int NOT NULL,
  `data` date NOT NULL,
  `energy` decimal(10,2) NOT NULL,
  `energyStr` varchar(4) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `moneyStr` varchar(4) NOT NULL,
  `gridPurchasedEnergy` float DEFAULT NULL,
  `gridPurchasedIncome` float DEFAULT NULL,
  `homeLoadEnergy` float DEFAULT NULL,
  `consumeEnergy` float DEFAULT NULL,
  `produceEnergy` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_dia_equip` (`fk_id_equipamento`,`data`),
  KEY `fk_id_equipamento_diario_idx` (`fk_id_equipamento`) USING BTREE /*!80000 INVISIBLE */,
  CONSTRAINT `fk_id_equipamento_diario` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5867679 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblsolismensal`
--

DROP TABLE IF EXISTS `tblsolismensal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblsolismensal` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_equipamento` int NOT NULL,
  `data` date NOT NULL,
  `energy` decimal(10,2) NOT NULL,
  `energyStr` varchar(4) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `moneyStr` varchar(4) NOT NULL,
  `gridPurchasedEnergy` float DEFAULT NULL,
  `gridPurchasedIncome` float DEFAULT NULL,
  `homeLoadEnergy` float DEFAULT NULL,
  `consumeEnergy` float DEFAULT NULL,
  `produceEnergy` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_fk_id_equip_solis_mensal` (`fk_id_equipamento`,`data`),
  KEY `fk_id_equipamento_idx` (`fk_id_equipamento`),
  CONSTRAINT `fk_id_equipamento_mensal` FOREIGN KEY (`fk_id_equipamento`) REFERENCES `tblequipamentos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=380110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tbltemplate`
--

DROP TABLE IF EXISTS `tbltemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbltemplate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_biblioteca` int NOT NULL,
  `numero` int NOT NULL,
  `descricao` varchar(100) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `fk_id_biblioteca_idx` (`fk_id_biblioteca`),
  CONSTRAINT `fk_id_biblioteca` FOREIGN KEY (`fk_id_biblioteca`) REFERENCES `tblbiblioteca` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tblvariaveis`
--

DROP TABLE IF EXISTS `tblvariaveis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tblvariaveis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fk_id_canais` int NOT NULL,
  `fk_id_template` int DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `unidade` varchar(10) NOT NULL,
  `casas` int DEFAULT '2',
  `tipo` int NOT NULL COMMENT 'tipo: \\n0 = Aquisição por tempo\\n1 = Aquisição por mudança',
  `numero` int NOT NULL COMMENT 'Será utilizando em canais do tipo rede. Se refere ao index do pacote.',
  `meta` float DEFAULT '0',
  `fator` float NOT NULL DEFAULT '0',
  `custo` float NOT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  `dt_acumulado` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_id_canal_idx` (`fk_id_canais`),
  KEY `fk_id_template_idx` (`fk_id_template`),
  CONSTRAINT `fk_id_canal` FOREIGN KEY (`fk_id_canais`) REFERENCES `tblcoletorcanais` (`id`),
  CONSTRAINT `fk_id_template` FOREIGN KEY (`fk_id_template`) REFERENCES `tbltemplate` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-04 12:34:04
