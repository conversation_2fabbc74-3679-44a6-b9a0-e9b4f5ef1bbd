<!DOCTYPE html>
<html lang='en' style="height: 100%;">

<head>
	<meta charset='utf-8'>
	<meta name='viewport' content='width=device-width, initial-scale=1.0'>   
	<!-- Incluindo Bootstrap JS (opcional, necessário apenas para recursos interativos) -->
	<script src="./js/jquery.min.js"></script>
	<script src="./js/popper.min.js"></script>
	<script src="./js/bootstrap.min.js"></script>
	<script src="js/webcc.min.js"></script>
  	<script src="js/Chart.min.js"></script>
	<script src="js/chartjs-adapter-date-fns.min.js"></script>
	<!-- <script src="js/pt-BR.js"></script> -->

	<!-- Estilos CSS -->
	<link rel="stylesheet" href="./css/bootstrap.min.css">
	<link rel="stylesheet" href="./css/styles.css">
	 
</head>

<body>
  	<!-- Adiciona uma div contendo o gráfico de barras -->  
	<div class="container mt-5">
		<canvas id="powerChart"></canvas>
	</div>

	<script>
	// Variaveis principais 
	let WinCCDados = ''
	let auxData = ""
	let props = ""	
	let powerChart = null	

	// Initialize the custom control (without a successful initialization, the CWC will remain empty. Make sure to include the webcc.min.js script!)
	WebCC.start(
		function (result) {
			if (result) {		
				if (WebCC.isDesignMode) {
					WinCCDados = JSON.parse(`{"dados":[{"x":"00:00:00","y":140},{"x":"10:00:00","y":1426},{"x":"12:00:00","y":845},{"x":"16:00:00","y":2498},
													{"x":"20:00:00","y":180},{"x":"21:30:00","y":469}],
												"propriedades":{"label":"Potência (kW)","Color":"rgba(75, 192, 192, 0.1)","borderColor":"rgba(135, 206, 250, 1)"}}`)
					drawTrend(true)
				} else {
					// Carrega as variáveis vindas do WinCC
					setProperty({ key: "Data", value: WebCC.Properties.Data})
					WebCC.onPropertyChanged.subscribe(setProperty);	
				}
			} else {
				console.log("connection failed")
				WinCCDados = {
    				dados: [
						{x: '00:00:00', y: 140},
						{x: '10:00:00', y: 1426},
						{x: '12:00:00', y: 845},
						{x: '16:00:00', y: 2498},
						{x: '20:00:00', y: 180},
						{x: '21:30:00', y: 469}

					],
    				propriedades: {
        				label : 'Potência (kW)',
        				Color: 'rgba(75, 192, 192, 0.1)',
        				borderColor: 'rgba(135, 206, 250, 1)'
    				}
				}
				modeDesigner = true
				setProperty({ key: "Data", value: JSON.stringify(WinCCDados)})
			}
				
		},
		// contract (same as manifest.json)
		{
			// Methods
			methods: {},

			// Events
			events: ['Update'],

			// Properties
			properties: {
				Data: ""
			}
		},
		// placeholder to include additional Unified dependencies (not used in this example)
		[],
		// connection timeout
		10000
	);

	function setProperty(data) {
		switch (data.key) {
			case "Data":
				if ((data.value) !== null && (data.value) !== undefined && (data.value) !== "") {
					if (powerChart !== null){
						// Destroi o gráfico anterior
						powerChart.destroy();
					}
					WinCCDados = JSON.parse(data.value)
					auxData = WinCCDados.dados
					props = WinCCDados.propriedades
					drawTrend(true)
					break;
				}
		}
	}

	function drawTrend(triggeredByTag) {
		// Aqui contém os dados do gráfico
		let dados = {
			datasets: [{
				type: 'line',
				label: props.label,
				data: auxData,
				borderColor: props.borderColor,
				backgroundColor: props.Color, 
				fill: true, 
				pointRadius: 0
			}]
		}

		// Configurações do gráfico
		const config = {
			type: 'line',
			data: dados,
			options: {
				interaction: {
					mode: 'index',
					intersect: false,
				},
				scales: {
					x: {
						type: 'time',
						time: {
							parser: 'HH:mm:ss',
							unit: 'hour',
							stepSize: 2,
							displayFormats: {
								hour: 'HH:mm' // Formato de hora AM/PM
							}
						},
						title: {
							display: true,
							text: 'Hora do Dia'
						},
						min: '00:00:00',
						max: '23:59:59',
						ticks: {
							source: 'auto',
							autoSkip: true,
							maxRotation: 0,
							minRotation: 0,
							maxTicksLimit: 12
						},
						grid: {
							display: false,
						}
					},
					y: {
						beginAtZero: true,
						title: {
							display: true,
							text: props.label
						},
						grid: {
							display: false, 
						}
					}
				},
				plugins: {
					legend: {
						display: false,
						position: 'bottom'
					},
					tooltip: {
						intersect: false,
						mode: 'index',
						callbacks: {
							title: function(tooltipItem) {
								let hourUnit = parseInt(tooltipItem[0].label.split(',')[2].split(':')[0])
								let minuteUnit = tooltipItem[0].label.split(',')[2].split(':')[1]
								let secondUnit = tooltipItem[0].label.split(',')[2].split(':')[2].slice(0, -4)
								let compl = tooltipItem[0].label.split(',')[2].slice(-4)
								if ((compl === 'p.m.') && (hourUnit !== 12)) {
									hourUnit = hourUnit + 12
								}
								return `Horário = ${hourUnit}:${minuteUnit}:${secondUnit}`
							},
							label: function(context) {
								return context.dataset.label + ': ' + context.formattedValue;
							}
						}
					},              
				},
				maintainAspectRatio: true,
				responsive: true,
				locale: 'pt-BR'
			}    
		}

		// Inicializa o gráfico
		powerChart = new Chart(document.getElementById('powerChart'), config);
	        
	}
	</script>

</body>

</html>