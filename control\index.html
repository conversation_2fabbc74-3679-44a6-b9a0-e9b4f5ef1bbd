<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Dashboard de Energia</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Estilos CSS -->
    <link rel="stylesheet" href="./css/style.css">
</head>
<body>
    <div class="container">
        <!-- Seção L - Lista de valores -->
        <div class="box lista">
            <div class="title">Leituras Atuais</div>
            <div class="table-container">
                <table class="table table-hover modern-table" id="dataTable">
                    <thead class="table-header">
                        <tr>
                            <th scope="col">Parâmetro</th>
                            <th scope="col">Valor</th>
                            <th scope="col">Unidade</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- Dados serão inseridos via JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Seção C - Cards -->
        <div class="box cards">
            <div class="title">Cards de Consumo</div>
            <div class="cards-container"></div>
        </div>
        
        <!-- Seção B - Gráfico -->
        <div class="box grafico">
            <div class="title">Consumo de Energia</div>
        </div>
        
        <!-- Seção M - Métricas -->
        <div class="box metricas">
            <div class="title">Médias de Consumo</div>
        </div>
        
        <!-- Seção G - Gauge -->
        <div class="box gauge">
            <div class="title">Parâmetros</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script principal -->
    <script src="./js/main.js"></script>
</body>
</html>
