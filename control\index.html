<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Dashboard de Energia</title>
    
    <!-- Estilos CSS -->
    <link rel="stylesheet" href="./css/style.css">
</head>
<body>
    <div class="container">
        <!-- Seção L - Lista de valores -->
        <div class="box lista">
            <div class="title">Leituras Atuais</div>
        </div>
        
        <!-- Seção C - Cards -->
        <div class="box cards">
            <div class="title">Cards de Consumo</div>
            <div class="cards-container">
                <div class="card">Card 1</div>
                <div class="card">Card 2</div>
                <div class="card">Card 3</div>
                <div class="card">Card 4</div>
                <div class="card">Card 5</div>
                <div class="card">Card 6</div>
                <div class="card">Card 7</div>
                <div class="card">Card 8</div>
            </div>
        </div>
        
        <!-- Seção B - Gráfico -->
        <div class="box grafico">
            <div class="title">Consumo de Energia</div>
            <p>Área para gráfico de consumo</p>
            <div style="text-align: center; margin-top: 10px;">
                <button>Dia</button>
                <button>Mês</button>
                <button>Ano</button>
                <button>Global</button>
            </div>
        </div>
        
        <!-- Seção M - Métricas -->
        <div class="box metricas">
            <div class="title">Médias de Consumo</div>
            <p>Área para métricas e médias de consumo</p>
            <p>Aqui serão exibidas estatísticas e médias</p>
            <div style="position: absolute; bottom: 10px; left: 10px; font-size: 12px;">
                Last: 2024-05-03 15:00:00
            </div>
        </div>
        
        <!-- Seção G - Gauge -->
        <div class="box gauge">
            <div class="title">Parâmetros</div>
            <p>Área para gráfico gauge</p>
            <p>Aqui será exibido o medidor tipo gauge</p>
        </div>
    </div>
</body>
</html>
