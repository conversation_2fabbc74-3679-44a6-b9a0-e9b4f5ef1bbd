<!DOCTYPE html>
<html lang='en' style="height: 100%;">

<head>
	<meta charset='utf-8'>
	<meta name='viewport' content='width=device-width, initial-scale=1.0'>
	   
	<!-- Incluindo Bootstrap JS (opcional, necessário apenas para recursos interativos) -->
	<script src="./js/jquery.min.js"></script>
	<script src="./js/popper.min.js"></script>
	<script src="./js/bootstrap.min.js"></script>
	<script src="js/webcc.min.js"></script>
  	<script src="js/Chart.min.js"></script>

	<!-- Estilos CSS -->
	<style>
		canvas {
		  width: 100% !important; /* Ajuste para que o canvas ocupe a largura total disponível */
		  max-width: 800px; /* Limite máximo de largura para maior responsividade */
		  height: auto !important; /* Altura automática para manter a proporção */
		  margin: 0 auto; /* Centraliza o gráfico horizontalmente */
		}
	</style>  
</head>

<body>
  <!-- Adiciona uma div contendo o gráfico de barras -->  
  <div class="container">
    <canvas id="myChart"></canvas>
  </div>

	<script>
	// Variaveis principais 
	let WinCCDados = ''
    let WinCCPropriedades = ''	
	let auxData = ""
	let props = ""	
	let myChart = null	

	// Initialize the custom control (without a successful initialization, the CWC will remain empty. Make sure to include the webcc.min.js script!)
	WebCC.start(
		function (result) {
			if (result) {		
				if (WebCC.isDesignMode) {
					WinCCDados = JSON.parse(`{"dados":{"Janeiro":65,"Fevereiro":59,"Março":80,"Abril":81,"Maio":56,"Junho":55,"Julho":40,"Agosto":60,"Setembro":55,"Outubro":45,"Novembro":30,"Dezembro":20},
											"propriedades":{"labelBar":"Potência gerada por Mês","widthBar":40,"ColorBar":"rgba(135, 206, 250, 0.5)","BorderColorBar":"rgba(135, 206, 250, 1)",
													"HoverColorBar":"rgba(54, 162, 235, 0.5)","HoverBorderBar":"rgba(54, 162, 235, 1)","LineColor":"rgba(135, 206, 250, 1)","TitleY":"KW"}}`)
					drawBars(true)
				} else {
					// Carrega as variáveis vindas do WinCC
					//WinCCPropriedades = WebCC.Properties.BarProperties
					setProperty({ key: "Data", value: WebCC.Properties.Data})
					WebCC.onPropertyChanged.subscribe(setProperty);	
				}
			} else {
				console.log("connection failed")
				WinCCDados = {
    				dados: {
        				Janeiro: 65,
        				Fevereiro: 59,
        				Março: 80,
        				Abril: 81,
        				Maio: 56,
        				Junho: 55,
        				Julho: 40,
        				Agosto: 60,
        				Setembro: 55,
        				Outubro: 45,   
        				Novembro: 30,
        				Dezembro: 20
    				},
    				propriedades: {
        				labelBar : 'Potência gerada por Mês',
        				widthBar: 40,
        				ColorBar: 'rgba(135, 206, 250, 0.5)',
        				BorderColorBar: 'rgba(135, 206, 250, 1)',
        				HoverColorBar: 'rgba(54, 162, 235, 0.5)',
        				HoverBorderBar : 'rgba(54, 162, 235, 1)',
        				LineColor: 'rgba(135, 206, 250, 1)',
        				TitleY: 'KW'
    				}
				}
				modeDesigner = true
				setProperty({ key: "Data", value: JSON.stringify(WinCCDados)})
			}
				
		},
		// contract (same as manifest.json)
		{
			// Methods
			methods: {},

			// Events
			events: ['Update'],

			// Properties
			properties: {
				Data: "",
				BarProperties: ""
			}
		},
		// placeholder to include additional Unified dependencies (not used in this example)
		[],
		// connection timeout
		10000
	);

	function setProperty(data) {
		switch (data.key) {
			case "Data":
				if ((data.value) !== null && (data.value) !== undefined && (data.value) !== "") {
					if (myChart !== null){
							myChart.destroy()
					}
					WinCCDados = JSON.parse(data.value)
					auxData = WinCCDados.dados
					props = WinCCDados.propriedades
					drawBars(true)
					break;
				}
		}
	}


	function drawBars(triggeredByTag) {
		let dados = {
			labels: Object.keys(auxData), // Aqui estamos coletando somente os dados do eixo X
			datasets: [
				{
					type: 'bar',
					label: props.labelBar,
					backgroundColor: props.ColorBar,
					borderColor: props.BorderColorBar,
					borderWidth: 1,
					hoverBackgroundColor: props.HoverColorBar,
					hoverBorderColor: props.HoverBorderBar,
					data: Object.values(auxData),
					barThickness: props.widthBar 
				},
				{
					type: 'line',
					label: 'Tendência',
					borderColor: props.LineColor, 
					borderWidth: 2,
					fill: false, 
					data: Object.values(auxData), 
					tension: 0 
				}
			]
		}

		const config = {
      		type: 'bar', // O tipo principal do gráfico é barra, mas inclui uma linha
      		data: dados,
      		options: {
        		maintainAspectRatio: false,
        		responsive: true,
        		plugins: {
          			legend: {
            			display: false
          			}
        		},
        		scales: {
          			y: {
            			beginAtZero: true,
            			grid: {
              				drawBorder: true,
              				drawOnChartArea: false
            			},
            			title: {
              				display: true,
              				text: props.TitleY
            			}
          			},
          			x: {
            			grid: {
              				drawBorder: true,
              				drawOnChartArea: false
            			}
          			}
        		}
      		}
    	}
		// Inicializa o gráfico
		myChart = new Chart(document.getElementById('myChart'), config);
	}
	</script>

</body>

</html>