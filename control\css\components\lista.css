/* ===================================
   COMPONENTE CSS - SEÇÃO L (LISTA)
   Dashboard de Energia - WinCC Unified
   =================================== */

/* Container da tabela */
.table-container {
    height: calc(100% - 50px);
    overflow-y: auto;
    padding: 0;
    border-radius: 5px;
    background-color: white;
}

/* Tabela principal */
.modern-table {
    margin-bottom: 0;
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
}

/* Classe table-centered para centralizar conteúdo */
.table-centered th,
.table-centered td {
    text-align: center;
    vertical-align: middle;
}

.table-centered th:first-child,
.table-centered td:first-child {
    text-align: left;
}

/* Cabeçalho da tabela */
.modern-table thead {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.modern-table thead th {
    color: #495057 !important;
    font-weight: 600 !important;
    padding: 12px 15px !important;
    border: none !important;
    font-size: 0.875rem !important;
    letter-spacing: 0.5px;
    background: transparent !important;
    border-bottom: 2px solid #dee2e6 !important;
    text-transform: uppercase;
}

.modern-table thead th:first-child {
    text-align: left !important;
    width: 65%;
}

.modern-table thead th:last-child {
    text-align: center !important;
    width: 35%;
}

/* Linhas da tabela */
.modern-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
}

.modern-table tbody tr:last-child {
    border-bottom: none;
}

/* Células da tabela */
.modern-table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
    color: #495057;
}

.modern-table tbody td:first-child {
    font-weight: 500;
    color: #212529;
    text-align: left;
    width: 65%;
}

.modern-table tbody td:last-child {
    font-weight: 600;
    color: #000000;
    text-align: center;
    width: 35%;
}

/* Scrollbar personalizada */
.table-container::-webkit-scrollbar {
    width: 6px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animação para as linhas da tabela */
@keyframes fadeInRow {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-row {
    animation: fadeInRow 0.3s ease-out forwards;
}

/* Estilos para o título com ícone */
.title-icon {
    color: #8e9aaf;
    font-size: 1.1em;
}
