/* ===================================
   COMPONENTE CSS - SEÇÃO L (LISTA)
   Dashboard de Energia - WinCC Unified
   =================================== */

/* Container da tabela */
.table-container {
    height: calc(100% - 50px);
    overflow-y: auto;
    padding: 0 5px;
    border-radius: 5px;
}

/* Tabela principal */
.modern-table {
    margin-bottom: 0;
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
}

/* Cabeçalho da tabela */
.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-header th {
    color: white !important;
    font-weight: 600;
    padding: 12px 8px;
    border: none;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table-header th:first-child {
    text-align: left;
    width: 65%;
}

.table-header th:last-child {
    text-align: center;
    width: 35%;
}

/* <PERSON><PERSON> da tabela */
.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Células da tabela */
.modern-table tbody td {
    padding: 8px;
    vertical-align: middle;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0;
}

.modern-table tbody td:first-child {
    font-weight: 500;
    color: #495057;
    text-align: left;
    padding-left: 12px;
    width: 65%;
    font-size: 0.9rem;
}

.modern-table tbody td:last-child {
    font-weight: bold;
    color: #000000;
    text-align: center;
    width: 35%;
    font-size: 0.95rem;
}

/* Scrollbar personalizada */
.table-container::-webkit-scrollbar {
    width: 6px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animação para as linhas da tabela */
@keyframes fadeInRow {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-row {
    animation: fadeInRow 0.5s ease-out forwards;
}

/* Estilos para o título com ícone */
.title-icon {
    color: #667eea;
    font-size: 1.1em;
}
