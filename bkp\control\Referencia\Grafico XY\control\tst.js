let obj = {
    dados: [
        {x: '00:00:00', y: 140},
		{x: '10:00:00', y: 1426},
		{x: '12:00:00', y: 845},
		{x: '16:00:00', y: 2498},
		{x: '20:00:00', y: 180},
		{x: '21:30:00', y: 469}
    ],
    propriedades: {
        label : 'Potência (kW)',
        Color: 'rgba(135, 206, 250, 0.5)',
        borderColor: 'rgba(135, 206, 250, 1)'
    }
}

// let texto = JSON.stringify(obj)
// let resp = JSON.parse(texto)

// console.log(resp.dados)

let tst = `{"dados":[{"x":"00:00:00","y":140},{"x":"10:00:00","y":1426},{"x":"12:00:00","y":845},{"x":"16:00:00","y":2498},{"x":"20:00:00","y":180},{"x":"21:30:00","y":469}],"propriedades":{"label":"Potência (kW)","Color":"rgba(75, 192, 192, 0.1)","borderColor":"rgba(135, 206, 250, 1)"}}`
let tst2 = JSON.parse(tst)
console.log(tst2.propriedades)

//console.log(Object.keys(obj)) // retorna um array dos atributos do objeto)