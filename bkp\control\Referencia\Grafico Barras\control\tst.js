let obj = {
    dados: {
        Janeiro: 65,
        <PERSON><PERSON>: 59,
        <PERSON><PERSON><PERSON>: 80,
        <PERSON><PERSON><PERSON>: 81,
        <PERSON><PERSON>: 56,
        <PERSON><PERSON>: 55,
        <PERSON><PERSON>: 40,
        <PERSON><PERSON><PERSON>: 60,
        <PERSON><PERSON><PERSON>: 55,
        <PERSON><PERSON><PERSON>: 45,   
        <PERSON><PERSON>bro: 30,
        <PERSON><PERSON><PERSON><PERSON>: 20
    },
    propriedades: {
        labelBar : 'Potência gerada por Mês',
        widthBar: 40,
        ColorBar: 'rgba(135, 206, 250, 0.5)',
        BorderColorBar: 'rgba(135, 206, 250, 1)',
        HoverColorBar: 'rgba(54, 162, 235, 0.5)',
        HoverBorderBar : 'rgba(54, 162, 235, 1)',
        LineColor: 'rgba(135, 206, 250, 1)',
        TitleY: 'KW'
    }
}

let texto = JSON.stringify(obj)
console.log(texto)

//console.log(Object.keys(obj)) // retorna um array dos atributos do objeto)