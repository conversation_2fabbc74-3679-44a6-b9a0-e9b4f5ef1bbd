<!DOCTYPE html>
<html lang='en' style="height: 100%;">

<head>
	<meta charset='utf-8'>
	<meta name='viewport' content='width=device-width, initial-scale=1.0'>
	   
	<!-- Incluindo Bootstrap JS (opcional, necessário apenas para recursos interativos) -->
	<script src="./js/jquery.min.js"></script>
	<script src="./js/popper.min.js"></script>
	<script src="./js/bootstrap.min.js"></script>
	<script src="js/webcc.min.js"></script>
  	<script src="js/echarts.min.js"></script>

	<!-- Estilos CSS -->
	<style>
        .gauge-container {
			height: 100%; 
			margin: 0;
        }
	</style>  
</head>

<body>
	<!-- Adiciona uma div contendo o gauge -->  
	<div class="container">
		<div class="gauge-container">
			<div id="gauge" style="width: 380px; height: 380px;"></div>
		</div>
	</div>

	<script>
		// Inicializa a instância do ECharts com base no DOM preparado
		let chartDom = document.getElementById('gauge');
		let myChart = echarts.init(chartDom);

		// Objeto contendo as propriedades do gauge
		let properties = {
			values: [75, 80, 60],
			colorMain: 'rgb(0, 151, 235)',
			colorMeta: 'green',
			colorFault: 'orange',
			colorGauge2: 'rgb(0, 75, 150)',
			colorGauge3: 'red',
			unit: '%',
			maxValue: 100,
			splitNumber: 5
		};

		// Inicializa o controle customizado
		WebCC.start(
			function (result) {
				if (result) {		
					if (WebCC.isDesignMode) {
						WinCCDados = `{"values":[75,80,60],"colorMain":"rgb(0, 151, 235)","colorMeta":"green","colorFault":"orange","colorGauge2":"rgb(0, 75, 150)","colorGauge3":"red","unit":"%","maxValue":100,"splitNumber":5, 
										"description": ["Descrição para Ponteiro 1", "Descrição para Ponteiro 2", "Descrição para Ponteiro 3"]}`
						updateGauge(JSON.parse(WinCCDados))
					} else {
						setProperty({ key: "Data", value: WebCC.Properties.Data})
						WebCC.onPropertyChanged.subscribe(setProperty);	
					}
				} else {
					console.log("connection failed")
					WinCCDados = {
						values: [30, 80, 60],
						colorMain: 'rgb(0, 151, 235)',
						colorMeta: 'green',
						colorFault: 'orange',
						colorGauge2: 'rgb(0, 75, 150)',
						colorGauge3: 'red',
						unit: '%',
						maxValue: 100,
						splitNumber: 5,
						description: ["Descrição para Ponteiro 1", "Descrição para Ponteiro 2", "Descrição para Ponteiro 3"]
					}
					modeDesigner = true
					setProperty({ key: "Data", value: JSON.stringify(WinCCDados)})
				}
			},
			{
				methods: {},
				events: ['Update'],
				properties: {
					Data: ""
				}
			},
			[],
			10000
		);

		function setProperty(data) {
			switch (data.key) {
				case "Data":
					if ((data.value) !== null && (data.value) !== undefined && (data.value) !== "") {
						WinCCDados = JSON.parse(data.value)
						updateGauge(WinCCDados)
					}
					break;
			}
		}

		// Função para atualizar o gauge com o objeto de propriedades
		function updateGauge(properties) {
			var mainValue = Number(properties.values[0]); // valor atual
			var faultValue = Number(properties.values[1]); // meta máxima
			var metaValue = Number(properties.values[2]); // média
			var newColor;

			if (mainValue > faultValue) {
				newColor = properties.colorGauge3;
			} else if (mainValue > metaValue) {
				newColor = properties.colorGauge2;
			} else {
				newColor = properties.colorMain;
			}

			var option = {
				tooltip: {
					trigger: 'item',
					backgroundColor: 'rgba(0, 0, 0, 0.7)',  // Fundo preto com transparência
					textStyle: {
						color: '#ffffff',  // Texto branco
					},
					formatter: function (params) {
						// Descrições personalizadas para cada ponteiro
						if (params.seriesIndex === 0) {
							return properties.description[0];
						} else if (params.seriesIndex === 1) {
							return properties.description[1];
						} else if (params.seriesIndex === 2) {
							return properties.description[2];
						}
					}
				},
				series: [
					{
						type: 'gauge',
						startAngle: 225,
						endAngle: -45,
						max: properties.maxValue,
						splitNumber: properties.splitNumber,
						progress: {
							show: true,
							width: 18,
							itemStyle: {
								color: newColor
							}
						},
						pointer: {
							itemStyle: {
								color: newColor
							}
						},
						axisLine: {
							lineStyle: {
								width: 18,
								color: [[1, '#e6e6e6']]
							}
						},
						axisTick: {
							show: false
						},
						splitLine: {
							length: 15,
							lineStyle: {
								width: 2,
								color: '#999'
							}
						},
						axisLabel: {
							distance: 25,
							color: '#999',
							fontSize: 12
						},
						anchor: {
							show: true,
							showAbove: true,
							size: 25,
							itemStyle: {
								borderWidth: 10,
								borderColor: newColor,
								color: '#ffffff'
							}
						},
						title: {
							show: true,
							offsetCenter: [0, '-30%'],
							fontSize: 20,
							formatter: '{value}' + properties.unit
						},
						detail: {
							valueAnimation: true,
							formatter: function(value) {
								return '{value|' + value + '}\n{unit|' + properties.unit + '}';
							},
							rich: {
								value: {
									fontSize: 30,
									fontWeight: 'bold',
									color: 'black'
								},
								unit: {
									fontSize: 20,
									color: 'black',
									padding: [5, 0, 0, 0]
								}
							},
							offsetCenter: [0, '40%']
						},
						data: [
							{
								value: mainValue,
								name: ''
							}
						]
					},
					{
						type: 'gauge',
						startAngle: 225,
						endAngle: -45,
						max: properties.maxValue,
						splitNumber: properties.splitNumber,
						pointer: {
							length: '90%',
							width: 4,
							itemStyle: {
								color: properties.colorFault
							}
						},
						anchor: {
							show: true,
							showAbove: true,
							size: 15,
							itemStyle: {
								borderWidth: 5,
								borderColor: 'white',
								color: '#ffffff'
							}
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false
						},
						axisLabel: {
							show: false
						},
						detail: {
							show: false
						},
						data: [
							{
								value: faultValue,
								name: ''
							}
						]
					},
					{
						type: 'gauge',
						startAngle: 225,
						endAngle: -45,
						max: properties.maxValue,
						splitNumber: properties.splitNumber,
						pointer: {
							length: '90%',
							width: 4,
							itemStyle: {
								color: properties.colorMeta
							}
						},
						anchor: {
							show: true,
							showAbove: true,
							size: 15,
							itemStyle: {
								borderWidth: 5,
								borderColor: 'white',
								color: '#ffffff'
							}
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false
						},
						axisLabel: {
							show: false
						},
						detail: {
							show: false
						},
						data: [
							{
								value: metaValue,
								name: ''
							}
						]
					}
				]
			};

			// Define o objeto configurado como as opções do gráfico
			myChart.setOption(option);
		}

	</script>
