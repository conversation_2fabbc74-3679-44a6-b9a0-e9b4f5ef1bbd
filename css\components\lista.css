/* ===================================
   COMPONENTE CSS - SEÇÃO L (LISTA)
   Dashboard de Energia - WinCC Unified
   Estilo: Mesa Simples (Tailwind-like)
   =================================== */

/* Container da tabela */
.table-container {
    height: calc(100% - 64px);
    overflow-y: auto;
    padding: 0;
    background-color: white;
}

/* Reset da tabela Bootstrap para estilo customizado */
#dataTable {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

/* Cabeçalho da tabela */
#dataTable thead th {
    background-color: #f9fafb !important;
    border-bottom: 1px solid #e5e7eb !important;
    border-top: none !important;
    color: #374151 !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    padding: 12px 16px !important;
    text-align: left !important;
}

#dataTable thead th:first-child {
    border-left: 1px solid #e5e7eb !important;
    border-top-left-radius: 8px !important;
}

#dataTable thead th:last-child {
    border-right: 1px solid #e5e7eb !important;
    border-top-right-radius: 8px !important;
    text-align: center !important;
}

/* Corpo da tabela */
#dataTable tbody tr {
    border-bottom: 1px solid #e5e7eb !important;
    transition: all 0.15s ease !important;
}

#dataTable tbody tr:hover {
    background-color: #f9fafb !important;
}

#dataTable tbody tr:last-child {
    border-bottom: none !important;
}

#dataTable tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px !important;
}

#dataTable tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px !important;
}

/* Células da tabela */
#dataTable tbody td {
    padding: 16px !important;
    font-size: 0.875rem !important;
    color: #111827 !important;
    border-left: 1px solid #e5e7eb !important;
    border-right: 1px solid #e5e7eb !important;
    vertical-align: middle !important;
}

#dataTable tbody td:first-child {
    font-weight: 500 !important;
    color: #111827 !important;
}

#dataTable tbody td:last-child {
    text-align: center !important;
    color: #6b7280 !important;
    font-weight: 400 !important;
}

/* Linha selecionada */
#dataTable tbody tr.selected {
    background-color: #eff6ff !important;
    border-color: #3b82f6 !important;
}

#dataTable tbody tr.selected td {
    border-color: #3b82f6 !important;
}

#dataTable tbody tr.selected td:first-child {
    position: relative;
}

#dataTable tbody tr.selected td:first-child::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #3b82f6;
    border-radius: 50%;
    margin-right: 8px;
}

/* Estilos para o título com ícone */
.title-icon {
    color: #6b7280;
    font-size: 1.1em;
}

/* Padding do título */
.lista .title {
    padding: 16px 20px !important;
    background-color: white;
    border-bottom: 1px solid #e5e7eb;
    margin: -10px -10px 0 -10px !important;
    color: #111827 !important;
    font-weight: 600 !important;
}
