# 📋 **RESUMO COMPLETO DO PROJETO - Sistema de Cadastro AstraZeneca**

## 🎯 **Visão Geral do Sistema**
Sistema web de cadastro e gerenciamento de equipamentos industriais, coletores de dados, canais de comunicação e variáveis de processo. 
O sistema permite configurar a infraestrutura de coleta de dados de equipamentos conectados via diferentes tipos de canais (Hardware IO, Rede Modbus, etc.).

## 🏗️ **Arquitetura do Sistema**

### **Frontend:**
- **HTML5** com Bootstrap 5 para interface responsiva
- **JavaScript ES6** modular organizado por funcionalidades
- **CSS3** componentizado para estilização específica

### **Backend/Dados:**
- Integração com **WinCC** (sistema SCADA da Siemens)
- Dados armazenados em formato JSON
- Comunicação via eventos JavaScript com WinCC

### **Estrutura de Arquivos:**
```
control/
├── index.html                 # Interface principal
├── css/
│   ├── style.css             # Estilos globais
│   └── components/           # Estilos por componente
│       ├── common.css
│       ├── coletores.css
│       ├── equipamentos.css
│       ├── variaveis.css
│       └── coletorCanais.css
└── js/
    ├── main.js               # Inicialização e dados globais
    ├── utils.js              # Funções utilitárias
    └── modules/              # Módulos por funcionalidade
        ├── coletores.js
        ├── equipamentos.js
        ├── locais.js
        ├── coletorCanais.js
        └── variaveis.js
```

## 📊 **Estrutura de Dados (Tabelas)**

### **Tabelas Principais:**
1. **`tblcoletores`** - Coletores de dados (Hardware/API)
2. **`tblequipamentos`** - Equipamentos industriais
3. **`tblcanais`** - Canais de comunicação (CONTADOR, DIGITAL, ANALOGICO, REDE)
4. **`tblcoletorCanais`** - Associações entre coletores, equipamentos e canais
5. **`tblvariaveis`** - Variáveis de processo coletadas
6. **`tbllocais`** - Localizações físicas dos equipamentos
7. **`tblbiblioteca`** - Bibliotecas de equipamentos
8. **`tbltemplate`** - Templates de variáveis por biblioteca
9. **`tblsoftware`** - Configurações e parâmetros do sistema

### **Relacionamentos Principais:**
- **Equipamento** → pertence a uma **Biblioteca** e **Local**
- **Associação** → conecta **Coletor + Equipamento + Canal**
- **Variável** → pertence a um **Equipamento** e **Associação**, pode usar **Template**
- **Template** → pertence a uma **Biblioteca**

## 🔧 **Funcionalidades por Aba**

### **1. Aba Coletores**
- Cadastro de coletores (Hardware com IP ou API com credenciais)
- Tipos: Hardware (IO físico) e API (comunicação rede)
- Validação de endereços IP para tipo Hardware

### **2. Aba Equipamentos**
- Cadastro de equipamentos industriais
- Vinculação obrigatória a Biblioteca e Local
- Campos: Nome, TAG, Fabricante, Modelo
- **REMOVIDO**: Campo Canal (última modificação)

### **3. Aba Associação (Coletor-Canais)**
- **Funcionalidade crítica** do sistema
- Associa: Coletor + Equipamento + Canal
- Parâmetros específicos por tipo de canal:
  - **REDE**: 5 parâmetros (Endereço Slave, Endereço Inicial, Length, Estrutura, Formato)
  - **ANALOGICO**: 2 parâmetros
  - **CONTADOR**: 1 parâmetro
  - **DIGITAL**: sem parâmetros específicos
- Botão "+" para criar variáveis diretamente da associação
- Filtros por Coletor, Equipamento e Tipo de Canal

### **4. Aba Variáveis**
- Cadastro de variáveis de processo
- **Sistema de Templates implementado**:
  - Templates filtrados por biblioteca do equipamento
  - Preenchimento automático: Nome, Unidade, Casas, Fator
  - Campo Template opcional (FK_ID_TEMPLATE)
- Coluna Biblioteca (baseada no template selecionado)
- Validação especial para canais REDE (campo Índice)
- Filtros por Coletor, Equipamento e Tipo de Canal

### **5. Aba Locais**
- Cadastro simples de localizações físicas
- Usado para organizar equipamentos geograficamente

## 🎯 **Regras de Negócio Importantes**

### **Associações (Coletor-Canais):**
- Cada canal pode ser usado apenas uma vez por coletor
- Canais REDE: até 16 variáveis por associação
- Outros canais: apenas 1 variável por associação

### **Variáveis:**
- Podem usar template da biblioteca do equipamento (opcional)
- Se usar template: coluna Biblioteca preenchida
- Se não usar template: coluna Biblioteca vazia
- Validação de índice para canais REDE (0 ≤ índice < Length)

### **Templates:**
- Pertencem a bibliotecas específicas
- Preenchem automaticamente: Nome, Unidade, Casas, Fator
- Usuário pode editar campos após preenchimento automático

## 🔄 **Fluxo de Trabalho Típico**
1. **Cadastrar Locais** (onde ficam os equipamentos)
2. **Cadastrar Coletores** (Hardware ou API)
3. **Cadastrar Equipamentos** (vinculados a Biblioteca e Local)
4. **Criar Associações** (Coletor + Equipamento + Canal)
5. **Criar Variáveis** via botão "+" das associações (com ou sem template)

## 🚀 **Últimas Modificações Implementadas**
- ✅ Remoção dos campos Template e Principal das variáveis
- ✅ Implementação do sistema de Templates da biblioteca
- ✅ Adição da coluna Biblioteca no grid de variáveis
- ✅ Preenchimento automático de campos via template
- ✅ Remoção da coluna Canal dos equipamentos
- ✅ Correção da ordem das colunas no grid de variáveis

## 📝 **Estado Atual**
O sistema está **funcional e estável** com todas as funcionalidades principais implementadas. A estrutura modular permite fácil manutenção e extensão de funcionalidades.
